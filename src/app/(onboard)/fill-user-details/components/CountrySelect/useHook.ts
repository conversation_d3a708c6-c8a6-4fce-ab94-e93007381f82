import { fetchCountries } from "@/networks/data/country";
import { CountriesI, FetchCountryResponseI } from "@/networks/data/types";
import { SetStateAction, useEffect, useState } from "react";



const useCountries = () => {

    const [countries, setCountries] = useState<SetStateAction<CountriesI[]>>([])

    const fetchCountriesAsync = async (): Promise<FetchCountryResponseI | void> => {
        try {
            const params = { page: "1" };
            const result = await fetchCountries(params)
            setCountries(result.data);
            return result;
        } catch (error) {
            console.error('Error fetching countries:', error);
        }
    };

    useEffect(() => {
        fetchCountriesAsync()
    }, [])


    return {
        countries,
    }
}

export default useCountries;